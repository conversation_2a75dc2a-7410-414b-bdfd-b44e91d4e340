/**
 * Test script to verify authentication fixes
 * This can be run in the browser console to test the authentication flow
 */

import { useAuthStore } from '@/stores/auth.store';
import { validateAuthState, isTokenExpired } from './auth-validator';

export async function testAuthenticationFlow() {
  console.log('🔍 Testing TravelViz Authentication Flow');
  console.log('=====================================');

  const authStore = useAuthStore.getState();
  const { accessToken, refreshToken, isAuthenticated, user } = authStore;

  console.log('📊 Current Auth State:');
  console.log('- Authenticated:', isAuthenticated);
  console.log('- Has Access Token:', !!accessToken);
  console.log('- Has Refresh Token:', !!refreshToken);
  console.log('- User:', user?.email || 'None');

  if (accessToken) {
    console.log('\n🔐 Token Analysis:');
    
    try {
      const isExpired = isTokenExpired(accessToken);
      console.log('- Token Expired:', isExpired);
      
      // Decode token to show expiration time
      const parts = accessToken.split('.');
      if (parts.length === 3) {
        const payload = JSON.parse(atob(parts[1].replace(/-/g, '+').replace(/_/g, '/')));
        if (payload.exp) {
          const expTime = new Date(payload.exp * 1000);
          const now = new Date();
          const timeLeft = expTime.getTime() - now.getTime();
          
          console.log('- Expires At:', expTime.toISOString());
          console.log('- Current Time:', now.toISOString());
          console.log('- Time Left:', Math.round(timeLeft / 1000), 'seconds');
        }
      }
    } catch (error) {
      console.error('- Token decode error:', error);
    }
  }

  console.log('\n🧪 Testing Auth Validation:');
  
  try {
    const isValid = await validateAuthState();
    console.log('- Auth validation result:', isValid);
  } catch (error) {
    console.error('- Auth validation error:', error);
  }

  console.log('\n✅ Test Complete');
  
  return {
    isAuthenticated,
    hasAccessToken: !!accessToken,
    hasRefreshToken: !!refreshToken,
    userEmail: user?.email,
    tokenExpired: accessToken ? isTokenExpired(accessToken) : null
  };
}

/**
 * Test API call with current authentication
 */
export async function testApiCall() {
  console.log('🌐 Testing API Call with Current Auth');
  console.log('====================================');

  try {
    // Try to make a simple API call to test authentication
    const response = await fetch('/api/v1/trips?page=1&limit=1', {
      headers: {
        'Authorization': `Bearer ${useAuthStore.getState().accessToken}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('- Response Status:', response.status);
    console.log('- Response OK:', response.ok);

    if (response.status === 401) {
      console.log('❌ Authentication failed - token is invalid/expired');
      return { success: false, status: 401, message: 'Unauthorized' };
    }

    if (response.ok) {
      const data = await response.json();
      console.log('✅ API call successful');
      console.log('- Response data:', data);
      return { success: true, status: response.status, data };
    } else {
      const errorData = await response.text();
      console.log('❌ API call failed');
      console.log('- Error:', errorData);
      return { success: false, status: response.status, error: errorData };
    }
  } catch (error) {
    console.error('❌ API call error:', error);
    return { success: false, error: error instanceof Error ? error.message : String(error) };
  }
}

/**
 * Force token expiration for testing (development only)
 */
export function simulateTokenExpiration() {
  if (process.env.NODE_ENV !== 'development') {
    console.warn('Token expiration simulation only available in development');
    return;
  }

  console.log('⚠️ Simulating Token Expiration');
  console.log('==============================');

  const authStore = useAuthStore.getState();
  const { accessToken } = authStore;

  if (!accessToken) {
    console.log('No access token to expire');
    return;
  }

  try {
    // Create an expired token by modifying the expiration time
    const parts = accessToken.split('.');
    const header = parts[0];
    const payload = JSON.parse(atob(parts[1].replace(/-/g, '+').replace(/_/g, '/')));
    const signature = parts[2];

    // Set expiration to 1 hour ago
    payload.exp = Math.floor(Date.now() / 1000) - 3600;

    const newPayload = btoa(JSON.stringify(payload)).replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
    const expiredToken = `${header}.${newPayload}.${signature}`;

    // Update the store with the expired token
    authStore.setTokens(expiredToken, authStore.refreshToken);

    console.log('✅ Token expiration simulated');
    console.log('- New token expires at:', new Date(payload.exp * 1000).toISOString());
    console.log('- Token is expired:', isTokenExpired(expiredToken));

    return expiredToken;
  } catch (error) {
    console.error('Failed to simulate token expiration:', error);
  }
}

// Make functions available globally for browser console testing
if (typeof window !== 'undefined') {
  (window as any).testAuth = {
    testAuthenticationFlow,
    testApiCall,
    simulateTokenExpiration
  };
  
  console.log('🔧 Auth test functions available:');
  console.log('- window.testAuth.testAuthenticationFlow()');
  console.log('- window.testAuth.testApiCall()');
  console.log('- window.testAuth.simulateTokenExpiration()');
}
